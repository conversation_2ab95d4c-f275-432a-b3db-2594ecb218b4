import SwiftUI
import UIKit
import Combine
import OSLog
import Security
import Vision

// Add KeychainError enum
enum KeychainError: Error {
    case duplicateItem
    case itemNotFound
    case unexpectedStatus(OSStatus)
}

@MainActor
class QRCodeLoginViewModel: ObservableObject {
    @Published var qrCodeImage: UIImage?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var remainingSeconds: Int?
    @Published var loginStatus: LoginStatus = .idle
    @Published var verificationNumber: String?
    @Published var isUserLoggedIn: Bool = true
    @Published var userProfile: UserProfile?
    @Published var actionURL: String?
    @Published var actionButtonTitle: String.LocalizationValue?
    @Published var actionButtonImage: String?
    @Published var title: String.LocalizationValue
    
    // User profile struct to store user information
    struct UserProfile {
        let nickname: String
        let profileImageUrl: String?
    }

    // Enum for login status
    enum LoginStatus: Equatable {
        case idle
        case fetchingQR
        case waitingForScan(sessionID: String, appsession: String)
        case finalizingLogin(sessionID: String, appsession: String)
        case verifyingUserStatus
        case complete
        case expired
        case error(String)
    }

    private let loginPageURL = URL(string: "https://nid.naver.com/nidlogin.login?mode=qrcode&url=https%3A%2F%2Fchzzk.naver.com%2F&locale=ko_KR&svctype=1")!
    private let sseBaseURLString = "https://nid.naver.com/push/qrcode"
    private let userStatusURL = URL(string: "https://comm-api.game.naver.com/nng_main/v1/user/getUserStatus")!
    private var dataFetchCancellable: AnyCancellable?
    private var timerCancellable: AnyCancellable?
    private var sseTask: URLSessionDataTask? // For SSE connection
    private var urlSession: URLSession? // Session for SSE delegate
    private var receivedSSEData = Data() // Buffer for incoming SSE data
    private var finalizeLoginCancellable: AnyCancellable?
    private var verifyUserStatusCancellable: AnyCancellable?
    private var validationTask: Task<Void, Never>?
    private var cancellables = Set<AnyCancellable>()
    
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.example.ChzzkTV", category: "QRCodeLoginViewModel")
    private let qrCodeDuration = 180 // 3 minutes
    
    // Need to hold a strong reference to the delegate
    private var sseDelegate: SSEDelegate? 

    // Initialize SSE Session and Delegate
    init() {
#if os(tvOS)
        title = "Scan QR Code with Naver App"
#else
        title = ""
#endif
        // Initialize delegate first
        self.sseDelegate = SSEDelegate(parentViewModel: self)
        // Configure URLSession with the delegate
        self.urlSession = URLSession(configuration: .default, delegate: sseDelegate, delegateQueue: OperationQueue.main) 
        
        // Sink to trigger actions based on state changes
        $loginStatus
            .receive(on: DispatchQueue.main) // Ensure execution on main thread
            .sink { [weak self] status in
                if case .finalizingLogin(let sessionID, let appsession) = status {
                    self?.finalizeLogin(sessionID: sessionID, appsession: appsession)
                } else if case .verifyingUserStatus = status {
                    Task { @MainActor in
                        await self?.verifyUserStatus()
                    }
                }
            }
            .store(in: &cancellables) // Need a general cancellables set now
            
        // Load cached cookies on initialization
        Task { @MainActor in
            await loadCachedCookies()
        }
    }

    func fetchQRCode() {
        guard loginStatus != .fetchingQR else { return }

        // Cancel any ongoing operations
        cancelOperations()

        // isLoading = true // Use loginStatus instead
        loginStatus = .fetchingQR
        errorMessage = nil
        qrCodeImage = nil
        verificationNumber = nil
        actionURL = nil
        logger.info("Starting QR code fetch...: \(self.loginPageURL.absoluteString)")
        print(HTTPCookieStorage.shared.cookies?.description ?? "No cookies stored.")
        // Assign the *single* sink result to dataFetchCancellable
        dataFetchCancellable = URLSession.shared.dataTaskPublisher(for: loginPageURL)
            .map(\.data)
            .receive(on: DispatchQueue.global(qos: .userInitiated))
            .tryMap { [weak self] data -> (html: String, data: Data) in // Pass data along
                guard let self else { throw URLError(.cancelled) }
                guard let htmlString = String(data: data, encoding: .utf8) else {
                    self.logger.error("Failed to decode HTML string from data.")
                    throw URLError(.cannotDecodeContentData)
                }
                self.logger.debug("Successfully fetched HTML content.")
                print(HTTPCookieStorage.shared.cookies?.description ?? "No cookies stored.")
                return (html: htmlString, data: data) // Return tuple
            }
            .tryMap { [weak self] result -> (imageData: Data, sessionID: String, appsession: String, verificationNumber: String) in 
                guard let self else { throw URLError(.cancelled) }
                
                // Extract Image Source
                self.logger.debug("Attempting to extract QR code image source from HTML.")
                guard let imageSourceString = self.extractImageSource(from: result.html) else {
                    self.logger.error("Failed to extract QR code image source from HTML.")
                    throw URLError(.cannotParseResponse)
                }
                self.logger.info("Extracted QR code image source (first 100 chars): \(imageSourceString.prefix(100))")
                
                // Extract Session ID
                self.logger.debug("Attempting to extract session ID from HTML.")
                guard let sessionID = self.extractSessionID(from: result.html) else {
                    self.logger.error("Failed to extract session ID from HTML.")
                    throw URLError(.cannotParseResponse) // Or a specific error
                }
                 self.logger.info("Extracted session ID: \(sessionID)")

                // Extract App Session
                self.logger.debug("Attempting to extract appsession from HTML.")
                guard let appsession = self.extractAppSession(from: result.html) else {
                    self.logger.error("Failed to extract appsession from HTML.")
                    throw URLError(.cannotParseResponse)
                }
                self.logger.info("Extracted appsession: \(appsession)")

                // Extract Verification Number
                self.logger.debug("Attempting to extract verification number from HTML.")
                guard let verificationNumber = self.extractVerificationNumber(from: result.html) else {
                    self.logger.error("Failed to extract verification number from HTML.")
                    throw URLError(.cannotParseResponse) // Or a specific error
                }
                self.logger.info("Extracted verification number: \(verificationNumber)")

                // Decode Image Data (if Data URL)
                if imageSourceString.starts(with: "data:image") {
                    self.logger.info("Source is a Data URL. Attempting to decode base64.")
                    guard let imageData = self.decodeDataURL(imageSourceString) else {
                        self.logger.error("Failed to decode base64 data from Data URL.")
                        throw URLError(.cannotDecodeContentData)
                    }
                    self.logger.info("Successfully decoded base64 data.")
                    // Return all extracted pieces
                    return (imageData: imageData, sessionID: sessionID, appsession: appsession, verificationNumber: verificationNumber)
                } else {
                    // Handle case where src is a standard URL (currently unsupported in this flow)
                    self.logger.error("Image source was a standard URL, which is not handled in this flow.")
                    throw URLError(.unsupportedURL)
                }
            }
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.logger.error("Failed to fetch or process QR code data: \(error.localizedDescription)")
                    let errorMsg = "Failed to load QR code: \(error.localizedDescription)"
                    self?.errorMessage = errorMsg
                    self?.loginStatus = .error(errorMsg)
                    // self?.isLoading = false // Use loginStatus
                    self?.cancelTimer() 
                    self?.cancelSSEConnection() // Cancel SSE if setup failed
                }
            }, receiveValue: { [weak self] result in // result is (imageData, sessionID, appsession, verificationNumber)
                 guard let self else { return }
                 // Process the image data
                 self.qrCodeImage = UIImage(data: result.imageData)
                 self.verificationNumber = result.verificationNumber
                 // self.isLoading = false // Use loginStatus
                 
                 if self.qrCodeImage == nil {
                    let errorMsg = "Failed to decode QR code image."
                    self.logger.error("Failed to create UIImage from downloaded data.")
                    self.errorMessage = errorMsg
                    self.loginStatus = .error(errorMsg)
                    self.cancelTimer()
                    verificationNumber = nil
                 } else {
                     self.logger.info("Successfully fetched and processed QR code image. Starting timer and SSE connection.")
                     // Update status to waiting, storing sessionID and appsession
                     self.loginStatus = .waitingForScan(sessionID: result.sessionID, appsession: result.appsession)
                     self.startTimer() // Start visual countdown
                     self.startSSEConnection(sessionID: result.sessionID) // Start listening for login status
                     
#if !os(tvOS)
                     Task {
                         await self.extractURLFromQRCode(sessionId: result.sessionID)
                     }
#endif
                 }
            })
    }
    
    private func extractURLFromQRCode(sessionId: String) async {
        guard let qrCodeImage = qrCodeImage else {
            logger.warning("No QR code image available for URL extraction")
            return
        }
        
        logger.info("Extracting URL from QR code image")
        
        guard let image = qrCodeImage.cgImage else {
            return
        }
        
        var request = DetectBarcodesRequest()
        request.symbologies = [.qr]
        
        do {
            let results = try await request.perform(on: image)

            if let observation = results.first?.payloadString {
                logger.info("Successfully extracted URL from QR code: \(observation)")
                convertLoginURLs(observation)
            } else {
                logger.warning("No QR code payload found, using fallback URL")
                let fallbackUrl = "https://nid.naver.com/nidlogin.qrcode?mode=qrcode&qrcodesession=\(sessionId)"
                convertLoginURLs(fallbackUrl)
            }
        } catch {
            logger.error("Failed to perform Vision request: \(error.localizedDescription)")
            let fallbackUrl = "https://nid.naver.com/nidlogin.qrcode?mode=qrcode&qrcodesession=\(sessionId)"
            convertLoginURLs(fallbackUrl)
        }
    }
    
    private func convertLoginURLs(_ url: String) {
        guard let encodedUrl = url.addingPercentEncoding(withAllowedCharacters: .alphanumerics) else {
            actionURL = url
            return
        }

        let appScheme = "naversearchapp://"
        let appUrl = "\(appScheme)inappbrowser?version=6&target=6&url=\(encodedUrl)"

        // Test if the app scheme is available
        if let appSchemeURL = URL(string: appScheme), UIApplication.shared.canOpenURL(appSchemeURL) {
            logger.info("Naver Search app is available, using app scheme: \(appUrl)")
            actionURL = appUrl
            actionButtonTitle = "Open Naver App"
            actionButtonImage = "n.circle"
            title = "Log in Chzzk with the account signed in on the Naver app"
        } else {
            logger.info("Naver Search app not available, using fallback URL: \(url)")
            actionURL = url
            actionButtonTitle = "Open Safari"
            actionButtonImage = "safari"
            title = "First, log in to Naver on Safari, then tap the button below to complete your Chzzk login"
        }
    }
    
    private func startTimer() {
        cancelTimer() // Ensure any existing timer is stopped
        remainingSeconds = qrCodeDuration
        logger.info("Starting QR countdown timer from \(self.qrCodeDuration) seconds.")
        
        timerCancellable = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
            .sink { [weak self] _ in
                guard let self else { return }
                
                if let currentSeconds = self.remainingSeconds, currentSeconds > 0 {
                    self.remainingSeconds = currentSeconds - 1
                } else {
                    self.logger.info("QR code timer expired.")
                    self.cancelTimer()
                    self.qrCodeImage = nil // Clear expired QR code
                    self.errorMessage = "QR code expired. Please refresh."
                    // Change status to expired instead of using error message
                    self.loginStatus = .expired
                }
            }
    }
    
    private func cancelTimer() {
        logger.debug("Cancelling timer.")
        timerCancellable?.cancel()
        timerCancellable = nil
        remainingSeconds = nil // Clear display when timer is not active
    }
    
    // --- SSE Methods --- 
    
    private func startSSEConnection(sessionID: String) {
        cancelSSEConnection() // Ensure previous task is cancelled
        
        guard let url = URL(string: "\(sseBaseURLString)?session=\(sessionID)") else {
            logger.error("Failed to create SSE URL.")
            loginStatus = .error("Internal error: Invalid SSE URL")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        request.cachePolicy = .reloadIgnoringLocalCacheData // Ensure fresh connection
        // Add timeout? SSE might run long
        // request.timeoutInterval = ... 
        
        logger.info("Starting SSE connection to \(url.absoluteString)")
        sseTask = urlSession?.dataTask(with: request)
        sseTask?.resume()
        
        // No initial status change here, wait for delegate callbacks or timeout/error
    }
    
    private func cancelSSEConnection() {
        if let task = sseTask, task.state == .running || task.state == .suspended {
             logger.info("Cancelling SSE connection task.")
             task.cancel()
        } else {
            logger.debug("No active SSE connection task to cancel or task already completed/cancelled.")
        }
        sseTask = nil
        receivedSSEData = Data() // Clear buffer
    }
    
    // Called by the SSEDelegate
    func processSSEData(_ data: Data) {
        receivedSSEData.append(data)
        // Try to process complete messages separated by \n\n
        while let range = receivedSSEData.range(of: Data([0x0a, 0x0a])) { // Check for \n\n
            let messageData = receivedSSEData.subdata(in: 0..<range.lowerBound)
            receivedSSEData.removeSubrange(0..<range.upperBound)
            
            parseSSEMessage(messageData)
        }
    }
    
    private func parseSSEMessage(_ messageData: Data) {
        guard let messageString = String(data: messageData, encoding: .utf8) else {
            logger.warning("Failed to decode SSE message data chunk to UTF8 string.")
            return
        }
        
        // Log the message after replacing newlines
        logger.debug("Parsing SSE message chunk: \(messageString.replacingOccurrences(of: "\n", with: " | "))")
        
        var eventType = "message" // Default event type
        var eventData: String? = nil
        // Simple parsing logic - assumes data is on a single line after 'data:'
        for line in messageString.split(separator: "\n", omittingEmptySubsequences: true) {
            if line.starts(with: "event:") {
                eventType = String(line.dropFirst("event:".count)).trimmingCharacters(in: .whitespaces)
            } else if line.starts(with: "data:") {
                 // Append if data is split across lines (basic handling)
                 let currentDataLine = String(line.dropFirst("data:".count)).trimmingCharacters(in: .whitespaces)
                 eventData = (eventData == nil) ? currentDataLine : (eventData! + "\n" + currentDataLine)
            } else if line.starts(with: "id:") {
                // logger.debug("SSE Event ID: \(line.dropFirst("id:".count).trimmingCharacters(in: .whitespaces))")
            } else if line.starts(with: "retry:") {
                // Handle retry logic if needed
            }
            // Ignore comments (lines starting with :) or empty lines already handled by split
        }
        
        guard eventType != "ping" else {
             // logger.debug("Ignoring SSE ping event.")
             return
         }
         
        guard let jsonDataString = eventData else {
            logger.warning("SSE message did not contain 'data:' field or data was empty.")
            return
        }
        
        logger.info("Received SSE Event Type: \(eventType), Data: \(jsonDataString)")
        
        guard let jsonData = jsonDataString.data(using: .utf8) else {
            logger.error("Failed to convert JSON string data back to Data.")
            return
        }
        
        do {
            // Assuming the structure is like: {"msgType": "COMPLETE", ...}
            if let jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
               let msgType = jsonObject["msgType"] as? String {
                
                logger.info("Parsed SSE msgType: \(msgType)")
                
                if msgType == "COMPLETE" {
                    logger.info("Login complete detected via SSE!")
                    // Transition to finalizing state, passing necessary IDs
                    if case .waitingForScan(let sessionID, let appsession) = loginStatus {
                         loginStatus = .finalizingLogin(sessionID: sessionID, appsession: appsession)
                    } else {
                        logger.error("SSE reported COMPLETE but loginStatus was not .waitingForScan. Current status: \(String(describing: self.loginStatus))")
                         loginStatus = .error("Internal state error after SSE completion.")
                    }
                    cancelTimer() // Stop visual timer
                    cancelSSEConnection() // Stop listening
                    // TODO: Need to handle authentication token/user info if provided in response
                } else {
                     logger.warning("Received unhandled msgType via SSE: \(msgType)")
                     // Handle other types like CANCELLED, TIMEOUT etc. if needed
                     // loginStatus = .error("Login failed: \(msgType)") ?
                 }
            } else {
                logger.error("Failed to parse SSE JSON data or find 'msgType' field.")
            }
        } catch {
            logger.error("Failed to deserialize SSE JSON data: \(error.localizedDescription). JSON String was: \(jsonDataString)")
        }
    }
    
    // Called by the SSEDelegate on completion/error
    func handleSSECompletion(error: Error?) {
        if let error = error as? URLError, error.code == .cancelled {
             logger.info("SSE task was cancelled normally.")
             return // Ignore cancellation errors
         }
          
         // Check if we're already in a completion flow - don't refresh if we're already
         // finalizing login, verifying status, or have completed/expired
         if case .finalizingLogin = loginStatus {
             logger.info("SSE connection closed while finalizing login - this is expected.")
             return // Already processing login, don't refresh
         }
         
         if case .verifyingUserStatus = loginStatus {
             logger.info("SSE connection closed while verifying user status - this is expected.")
             return // Already verifying user status, don't refresh
         }
         
         if case .complete = loginStatus {
             logger.info("SSE connection closed after login completed - this is expected.")
             return // Already completed, don't refresh
         }
         
         if case .expired = loginStatus {
             logger.info("SSE connection closed after QR code expired - this is expected.")
             return // Already expired, don't refresh
         }
          
         // Only handle unexpected closures if we're still waiting for scan
         if case .waitingForScan = loginStatus {
             if error != nil {
                 let errorMsg = "SSE connection ended unexpectedly: \(error?.localizedDescription ?? "Unknown error")"
                 logger.error("\(errorMsg)")
                 
                 // Automatically refresh QR code instead of showing error
                 logger.info("Automatically refreshing QR code after SSE connection error")
                 fetchQRCode()
             } else {
                 // SSE completed without error, but we didn't receive COMPLETE message
                 logger.warning("SSE connection closed without error before login completion was detected.")
                 // Automatically refresh QR code
                 logger.info("Automatically refreshing QR code after normal SSE completion without login")
                 fetchQRCode()
             }
         } else {
             // Some other unexpected state
             logger.warning("SSE connection closed. No action needed.")
         }
    }

    
    func cancelOperations() {
        logger.info("Cancelling all operations.")
        dataFetchCancellable?.cancel()
        dataFetchCancellable = nil
        validationTask?.cancel()
        validationTask = nil
        cancelTimer()
        cancelSSEConnection() // Ensure SSE task is cancelled
        // Reset state if needed
        if loginStatus != .idle {
           finalizeLoginCancellable?.cancel()
           verifyUserStatusCancellable?.cancel()
           loginStatus = .idle // Reset status on manual cancel
        }
    }
    
    deinit {
        logger.info("QRCodeLoginViewModel deinit - cancelling operations.")
        // Directly cancel tasks instead of calling actor-isolated method
        dataFetchCancellable?.cancel()
        timerCancellable?.cancel()
        sseTask?.cancel()
        validationTask?.cancel()
    }

    private func extractSessionID(from html: String) -> String? {
        // Regex to find <input type="hidden" id="qrcodesession" value="SESSION_ID"> (flexible with quotes and spacing)
        let pattern = "<input\\s+[^>]*id=[\"']qrcodesession[\"']\\s+[^>]*value=[\"']([^\"']+)['\"][^>]*>"
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            if let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.utf16.count)) {
                // Value is in the first capture group (index 1)
                if match.numberOfRanges > 1, let range = Range(match.range(at: 1), in: html) {
                    let sessionID = String(html[range])
                    logger.debug("Regex matched session ID: \(sessionID)")
                    return sessionID
                } else {
                    logger.warning("Regex matched qrcodesession input but failed to extract value capture group.")
                }
            } else {
                 logger.warning("Regex did not find a match for the qrcodesession input tag.")
            }
        } catch {
            logger.error("Regex error for session ID: \(error.localizedDescription)")
        }
        return nil
    }
    
    private func extractImageSource(from html: String) -> String? {
        // Pattern 0: Simplest - ANY img tag with a data URL source
        var pattern = "<img[^>]*src=[\"'](data:image/[^;]+;base64,[^\"']+)['\"][^>]*>"
        if let src = findSource(pattern: pattern, html: html, captureGroupIndex: 1) {
            logger.info("Found image source using simplest data URL pattern.")
            return src
        }

        // Pattern 1: Precise match for id="qrImage" and data:image/jpeg;base64
        logger.warning("Simplest pattern failed. Trying precise id='qrImage' and jpeg pattern.")
        pattern = "<img\\s+[^>]*id=['\"]qrImage['\"]\\s+[^>]*src=['\"](data:image\\/jpeg;base64,[^\"']+)['\"][^>]*>"
        if let src = findSource(pattern: pattern, html: html, captureGroupIndex: 1) {
            logger.info("Found image source using precise id='qrImage' and jpeg pattern.")
            return src
        }
        
        // Pattern 2: Original slightly broader ID pattern (any data:image)
        logger.warning("Precise jpeg pattern failed. Trying original ID pattern (qrImg|qrImage) with any data:image source.")
        pattern = "<img\\s+id=['\"](qrImg|qrImage)['\"]\\s+[^>]*src=['\"](data:image/[^;]+;base64,[^\"']+)['\"][^>]*>"
        if let src = findSource(pattern: pattern, html: html, captureGroupIndex: 2) {
            logger.info("Found image source using original ID pattern.")
            return src
        }
        
        // Pattern 3: Fallback for any data:image/png;base64 (less likely now)
        logger.warning("Original ID pattern failed. Trying fallback pattern for data:image/png;base64.")
        pattern = "<img\\s+[^>]*src=['\"](data:image\\/png;base64,[^\"']+)['\"][^>]*>"
         if let src = findSource(pattern: pattern, html: html, captureGroupIndex: 1) {
             logger.info("Found image source using fallback data URL pattern (png).")
             return src
         }

         logger.error("All regex patterns failed to find a suitable image source.")
         logger.error("HTML Content when failing:\\n---\\n\\(html)\\n---")
         return nil
    }
    
    private func findSource(pattern: String, html: String, captureGroupIndex: Int) -> String? {
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            if let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.utf16.count)) {
                // Log that a match was found
                logger.debug("Regex pattern '\\(pattern)' found a match.")
                if match.numberOfRanges > captureGroupIndex, let range = Range(match.range(at: captureGroupIndex), in: html) {
                     // Log successful group extraction
                     logger.debug("Successfully extracted capture group \\(captureGroupIndex).")
                    return String(html[range])
                } else {
                    // Log group extraction failure
                    logger.warning("Regex pattern '\\(pattern)' matched, but failed to extract capture group \\(captureGroupIndex). Number of ranges: \\(match.numberOfRanges)")
                }
            } else {
                // Log no match found
                logger.debug("Regex pattern '\\(pattern)' did not find any match in the HTML.")
            }
        } catch {
            logger.error("Regex error for pattern '\\(pattern)': \\(error.localizedDescription)")
        }
        // Log before returning nil from this function
        logger.debug("findSource returning nil for pattern '\\(pattern)'.")
        return nil
    }
    
    private func decodeDataURL(_ dataURLString: String) -> Data? {
        guard dataURLString.starts(with: "data:image"),
              let commaIndex = dataURLString.firstIndex(of: ","),
              commaIndex < dataURLString.endIndex else {
            logger.error("Invalid Data URL format.")
            return nil
        }
        
        var base64String = String(dataURLString[dataURLString.index(after: commaIndex)...])
        
        // Trim leading/trailing whitespace and newlines
        base64String = base64String.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Add detailed logging for the string before decoding
        logger.debug("Attempting to decode base64 string (after trimming).")
        logger.debug("Trimmed Base64 String Length: \(base64String.count)")
        logger.debug("Trimmed Base64 String Prefix (50 chars): \(base64String.prefix(50))")
        logger.debug("Trimmed Base64 String Suffix (50 chars): \(base64String.suffix(50))")
        
        // Attempt decoding with the trimmed string
        let decodedData = Data(base64Encoded: base64String)

        if decodedData == nil {
            logger.error("Data(base64Encoded:) returned nil. Base64 string might be invalid or contain unexpected characters.")
        }
        
        return decodedData
    }
    
    private func extractVerificationNumber(from html: String) -> String? {
        // Regex to find <strong class="point">NUMBER</strong>
        let pattern = "<strong\\s+class=[\"']point[\"']\\s*>\\s*(\\d+)\\s*</strong\\s*>"
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            if let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.utf16.count)) {
                // Number is in the first capture group (index 1)
                if match.numberOfRanges > 1, let range = Range(match.range(at: 1), in: html) {
                    let number = String(html[range])
                    logger.debug("Regex matched verification number: \(number)")
                    return number
                } else {
                    logger.warning("Regex matched verification number tag but failed to extract number capture group.")
                }
            } else {
                 logger.warning("Regex did not find a match for the verification number tag (<strong class='point'>).")
            }
        } catch {
            logger.error("Regex error for verification number: \(error.localizedDescription)")
        }
        return nil
    }
    
    // MARK: - Cookie Management
    
    func loadCachedCookies() async {
        logger.info("Loading cached cookies from Keychain")
        
        do {
            if let autCookie = try retrieveCookieFromKeychain(name: "NID_AUT"),
               let sesCookie = try retrieveCookieFromKeychain(name: "NID_SES") {
                
                // Add cookies to HTTPCookieStorage
                for cookie in [autCookie, sesCookie] {
                    HTTPCookieStorage.shared.setCookie(cookie)
                    logger.info("Loaded cached cookie: \(cookie.name) - \(cookie.value) - \(cookie.expiresDate ?? Date())")
                }
                
                // Verify logged in status immediately
                await validateCookies()
            } else {
                logger.info("No cached cookies found in Keychain")
                isUserLoggedIn = false
            }
        } catch {
            logger.error("Failed to load cached cookies: \(error.localizedDescription)")
            isUserLoggedIn = false
        }
    }

    func validateCookies() async {
        logger.info("Validating cookies by checking user status")
        await verifyUserStatus(forValidation: true)
    }
    
    func signOut() {
        logger.info("Signing out user")
        // Remove cookies from HTTPCookieStorage
        let cookieStorage = HTTPCookieStorage.shared
        if let cookies = cookieStorage.cookies {
            for cookie in cookies where cookie.name == "NID_AUT" || cookie.name == "NID_SES" {
                cookieStorage.deleteCookie(cookie)
            }
        }
        
        // Remove cookies from Keychain
        do {
            try deleteCookieFromKeychain(name: "NID_AUT")
            try deleteCookieFromKeychain(name: "NID_SES")
            logger.info("Successfully removed cookies from Keychain")
        } catch {
            logger.error("Failed to remove cookies from Keychain: \(error.localizedDescription)")
        }
        
        // Update state
        isUserLoggedIn = false
        userProfile = nil
        loginStatus = .idle
    }
    
    // MARK: - Keychain Operations
    
    private func saveCookieToKeychain(cookie: HTTPCookie) throws {
        // Convert cookie properties to serializable dictionary
        var cookieProperties = [String: Any]()
        if let properties = cookie.properties {
            for (key, value) in properties {
                if key == .expires, let expirationDate = value as? Date {
                    cookieProperties[key.rawValue] = expirationDate.timeIntervalSince1970
                } else {
                    cookieProperties[key.rawValue] = value
                }
            }
        }
        
        guard let cookieData = try? JSONSerialization.data(withJSONObject: cookieProperties) else {
            logger.error("Failed to serialize cookie properties")
            throw KeychainError.unexpectedStatus(errSecParam)
        }
        
        // Create query dictionary
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: cookie.name,
            kSecAttrService as String: "com.example.ChzzkTV.Cookies",
            kSecValueData as String: cookieData
        ]
        
        // Delete any existing item before saving
        SecItemDelete(query as CFDictionary)
        
        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            logger.error("Failed to save cookie to Keychain: \(status)")
            throw KeychainError.unexpectedStatus(status)
        }
        
        logger.info("Successfully saved cookie to Keychain: \(cookie.name)")
    }
    
    private func retrieveCookieFromKeychain(name: String) throws -> HTTPCookie? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: name,
            kSecAttrService as String: "com.example.ChzzkTV.Cookies",
            kSecReturnData as String: true
        ]
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status != errSecItemNotFound else {
            return nil
        }
        
        guard status == errSecSuccess else {
            logger.error("Failed to retrieve cookie from Keychain: \(status)")
            throw KeychainError.unexpectedStatus(status)
        }
        
        guard let cookieData = item as? Data,
              let cookiePropertiesDict = try? JSONSerialization.jsonObject(with: cookieData) as? [String: Any] else {
            logger.error("Failed to deserialize cookie properties")
            return nil
        }
        
        // Convert [String: Any] to [HTTPCookiePropertyKey: Any]
        var cookieProperties = [HTTPCookiePropertyKey: Any]()
        for (key, value) in cookiePropertiesDict {
            // Create the HTTPCookiePropertyKey directly
            let cookieKey = HTTPCookiePropertyKey(key)

            if cookieKey == .expires, let expiresValue = value as? Double {
                // Convert back to Date
                let expirationDate = Date(timeIntervalSince1970: expiresValue)
                cookieProperties[.expires] = expirationDate
            } else {
                cookieProperties[cookieKey] = value
            }
        }
        
        guard let cookie = HTTPCookie(properties: cookieProperties) else {
            logger.error("Failed to create cookie from properties")
            return nil
        }
        
        logger.info("Successfully retrieved cookie from Keychain: \(name)")
        return cookie
    }
    
    private func deleteCookieFromKeychain(name: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: name,
            kSecAttrService as String: "com.example.ChzzkTV.Cookies"
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            logger.error("Failed to delete cookie from Keychain: \(status)")
            throw KeychainError.unexpectedStatus(status)
        }
        
        logger.info("Successfully deleted cookie from Keychain: \(name)")
    }

    // --- Finalize Login (POST Request) --- 
    
    private func finalizeLogin(sessionID: String, appsession: String) {
        logger.info("Finalizing login by POSTing to nidlogin.login...")
        
        guard let url = URL(string: "https://nid.naver.com/nidlogin.login") else {
            logger.error("Invalid URL for finalize login POST.")
            loginStatus = .error("Internal error: Invalid login URL")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        // Use standard content type for form data
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type") 
        
        logger.debug("appsession: \(appsession), sessionId: \(sessionID)")
        
        // Construct the form payload - Use provided template and substitute dynamic values
        let payloadTemplate = "localechange=&dynamicKey=&sessionKey=&show_pk=true&wtoken=&svctype=1&bvsd=&locale=ko_KR&appsession=%@&url=https://chzzk.naver.com/&mode=qrcode&next_step=false&qrcodesession=%@&fbOff=true&smart_LEVEL=1&nvlong=on"
        let payloadString = String(format: payloadTemplate, appsession, sessionID)
        // Do NOT percent encode the entire payload for httpBody
        request.httpBody = payloadString.data(using: .utf8)

        logger.debug("Finalize login payload: \(payloadString)")
        print(HTTPCookieStorage.shared.cookies?.description ?? "No cookies stored.")
        // Use URLSession.shared for this one-off request (no delegate needed for now)
        finalizeLoginCancellable = URLSession.shared.dataTaskPublisher(for: request)
            .tryMap { data, response -> HTTPURLResponse in
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw URLError(.badServerResponse)
                }
                // We likely expect a redirect (3xx) or success (2xx)
                self.logger.info("Finalize login POST response status: \(httpResponse.statusCode)")
                // TODO: Check for specific status codes if needed
                return httpResponse
            }
            .map { httpResponse -> [HTTPCookie] in
                // Extract cookies from the response headers
                let cookies = HTTPCookie.cookies(withResponseHeaderFields: httpResponse.allHeaderFields as! [String: String], for: url)
                self.logger.info("Received \(cookies.count) cookies from finalize login POST.")
                for cookie in cookies {
                     self.logger.debug("- Cookie: \(cookie.name)=\(cookie.value); domain=\(cookie.domain); expires=\(cookie.expiresDate?.description ?? "N/A")")
                }
                // Store cookies in shared storage (important!)
                HTTPCookieStorage.shared.setCookies(cookies, for: url, mainDocumentURL: nil)
                
                // Cache essential cookies in Keychain
                for cookie in cookies where cookie.name == "NID_AUT" || cookie.name == "NID_SES" {
                    do {
                        try self.saveCookieToKeychain(cookie: cookie)
                    } catch {
                        self.logger.error("Failed to save cookie to Keychain: \(error.localizedDescription)")
                    }
                }
                
                return cookies
            }
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    let errorMsg = "Failed to finalize login: \(error.localizedDescription)"
                    self?.logger.error("\(errorMsg)")
                    self?.loginStatus = .error(errorMsg)
                }
            }, receiveValue: { [weak self] cookies in
                // Verify essential cookies exist (optional but recommended)
                let hasNIDAUT = cookies.contains { $0.name == "NID_AUT" }
                let hasNIDSES = cookies.contains { $0.name == "NID_SES" }
                if hasNIDAUT && hasNIDSES {
                     self?.logger.info("Essential cookies (NID_AUT, NID_SES) found. Proceeding to verify user status.")
                     
                     self?.loginStatus = .verifyingUserStatus // Move to next verification step
                } else {
                     let errorMsg = "Finalize login succeeded, but essential cookies (NID_AUT, NID_SES) were missing."
                     self?.logger.error("\(errorMsg)")
                     self?.loginStatus = .error(errorMsg)
                }
            })
    }
    
    private func extractAppSession(from html: String) -> String? {
        // Regex to find <input type="hidden" id="appsession" value="SESSION_ID"> (flexible with quotes and spacing)
        let pattern = "<input\\s+[^>]*id=[\"']appsession[\"']\\s+[^>]*value=[\"']([^\"']+)['\"][^>]*>"
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            if let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.utf16.count)) {
                // Value is in the first capture group (index 1)
                if match.numberOfRanges > 1, let range = Range(match.range(at: 1), in: html) {
                    let sessionID = String(html[range])
                    logger.debug("Regex matched appsession: \(sessionID)")
                    return sessionID
                } else {
                    logger.warning("Regex matched appsession input but failed to extract value capture group.")
                }
            } else {
                 logger.warning("Regex did not find a match for the appsession input tag.")
            }
        } catch {
            logger.error("Regex error for appsession: \(error.localizedDescription)")
        }
        return nil
    }

    // --- Verify User Status --- 
    
    func verifyUserStatus(forValidation: Bool = false) async {
        // If this is a validation call, cancel any existing validation task to prevent conflicts
        if forValidation {
            validationTask?.cancel()
            validationTask = Task { @MainActor in
                await performUserStatusVerification(forValidation: forValidation)
            }
            await validationTask?.value
            return
        }

        // For non-validation calls, proceed directly
        await performUserStatusVerification(forValidation: forValidation)
    }

    private func performUserStatusVerification(forValidation: Bool = false) async {
        logger.info("Verifying user status by GETting \(self.userStatusURL.absoluteString)")

        var request = URLRequest(url: userStatusURL)
        request.httpMethod = "GET"
        // Cookies should be sent automatically by URLSession from shared storage

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
                logger.error("User status request failed with status code: \(statusCode)")
                if forValidation {
                    isUserLoggedIn = false
                    userProfile = nil
                }
                return
            }
            
            // Check for and update NID_SES cookie from response headers
            if let setCookieHeader = httpResponse.allHeaderFields["Set-Cookie"] as? String {
                logger.info("Found Set-Cookie header: \(setCookieHeader)")
                let cookies = HTTPCookie.cookies(withResponseHeaderFields: ["Set-Cookie": setCookieHeader], for: self.userStatusURL)
                if let nidSesCookie = cookies.first(where: { $0.name == "NID_SES" }) {
                    logger.info("Found NID_SES cookie in response. Updating.")
                    HTTPCookieStorage.shared.setCookie(nidSesCookie)
                    
                    do {
                        try self.saveCookieToKeychain(cookie: nidSesCookie)
                    } catch {
                        self.logger.error("Failed to save cookie to Keychain: \(error.localizedDescription)")
                    }
                }
            }

            // Log the raw response for debugging
            let responseString = String(data: data, encoding: .utf8) ?? "(Could not decode response)"
            logger.info("User status response received: \(responseString)")
            
            let decoder = JSONDecoder()
            let userStatusResponse = try decoder.decode(UserStatusResponse.self, from: data)
            
            if userStatusResponse.content.loggedIn == true {
                logger.info("User status verified successfully. User is logged in. Nickname: \(userStatusResponse.content.nickname ?? "N/A")")
                isUserLoggedIn = true
                
                // Update user profile
                if let nickname = userStatusResponse.content.nickname {
                    userProfile = UserProfile(
                        nickname: nickname,
                        profileImageUrl: userStatusResponse.content.profileImageUrl
                    )
                }
                
                // Only update login status if not validating
                if !forValidation {
                    loginStatus = .complete // Final success!
                }
            } else {
                logger.warning("User status check returned loggedIn = false.")
                isUserLoggedIn = false
                userProfile = nil
                
                // Clear cached cookies since they're invalid
                do {
                    try deleteCookieFromKeychain(name: "NID_AUT")
                    try deleteCookieFromKeychain(name: "NID_SES")
                } catch {
                    logger.error("Failed to clear invalid cookies: \(error.localizedDescription)")
                }
                
                // Only update login status if not validating
                if !forValidation {
                    let errorMsg = "User status check returned loggedIn = false."
                    logger.error("\(errorMsg)")
                    loginStatus = .error(errorMsg)
                }
            }
        } catch {
            logger.error("Failed to verify user status: \(error.localizedDescription)")
            if forValidation {
                isUserLoggedIn = false
                userProfile = nil
            }
        }
    }
}

// Simple Delegate Class to handle SSE Callbacks
@MainActor
private final class SSEDelegate: NSObject, URLSessionDataDelegate {
    // Need a shared cancellables store in the ViewModel
    private var cancellables = Set<AnyCancellable>()
    
    weak var parentViewModel: QRCodeLoginViewModel?
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.example.ChzzkTV", category: "SSEDelegate")

    init(parentViewModel: QRCodeLoginViewModel) {
        self.parentViewModel = parentViewModel
    }

    nonisolated func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        logger.debug("SSE Delegate: didReceive data (\(data.count) bytes).")
        Task { @MainActor in
            parentViewModel?.processSSEData(data)
        }
    }

    nonisolated func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            logger.error("SSE Delegate: didCompleteWithError: \(error.localizedDescription)")
        } else {
            logger.info("SSE Delegate: didCompleteWithError: nil (Connection closed normally by server or client cancellation).")
        }
        Task { @MainActor in
            parentViewModel?.handleSSECompletion(error: error)
        }
    }
    
    // Optional: Handle response headers if needed
    /*
    nonisolated func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        if let httpResponse = response as? HTTPURLResponse {
            logger.debug("SSE Delegate: didReceive response - Status Code: \(httpResponse.statusCode)")
            // Check status code and headers, e.g., Content-Type
            if httpResponse.statusCode == 200 && httpResponse.mimeType == "text/event-stream" {
                 completionHandler(.allow) // Proceed
            } else {
                 logger.error("SSE Delegate: Invalid response - Status: \(httpResponse.statusCode), MIMEType: \(httpResponse.mimeType ?? "N/A")")
                 completionHandler(.cancel) // Cancel if response is not as expected
            }
        } else {
             completionHandler(.allow) // Or cancel if non-HTTP response is unexpected
        }
    }
    */
}

// --- Codable Structs for User Status Response --- 

struct UserStatusResponse: Codable {
    let code: Int
    let message: String?
    let content: UserStatusContent
}

struct UserStatusContent: Codable {
    let hasProfile: Bool?
    let userIdHash: String?
    let nickname: String?
    let profileImageUrl: String?
    let penalties: [String]? // Assuming array of strings, adjust if different
    let officialNotiAgree: Bool?
    let officialNotiAgreeUpdatedDate: String?
    let verifiedMark: Bool?
    let loggedIn: Bool?
} 
