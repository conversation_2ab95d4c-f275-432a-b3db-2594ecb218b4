import SwiftUI
import SwiftData

struct HomeView: View {    
    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.modelContext) private var modelContext
    @Environment(\.channelService) private var channelService
    @Environment(\.videoService) private var videoService
    
    @StateObject private var viewModel: HomeViewModel
    @State private var selectedLiveStream: UILiveStream? = nil
    @State private var selectedVideo: UIVideo? = nil
    @State private var isViewActive = false
    @State private var showSignOutAlert = false
#if os(tvOS)
    @State private var previousFocusSection: HomeSection?
    @FocusState private var focusSection: HomeSection?
    
    enum HomeSection {
        case live
        case vod
        case followers
    }
#endif
    
    init(
        channelService: ChannelServiceProtocol,
        liveService: LiveServiceProtocol,
        videoService: VideoServiceProtocol,
        loginViewModel: any LoginViewModelProtocol
    ) {
        let viewModel = HomeViewModel(
            channelService: channelService,
            liveService: liveService,
            videoService: videoService,
            loginViewModel: loginViewModel
        )
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                contentView
            }
#if !os(tvOS)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    ProfileIconView(
                        loginViewModel: viewModel.loginViewModel,
                        showSignOutAlert: $showSignOutAlert
                    )
                }
            }
            .refreshable {
                await viewModel.loadContent(forceRefresh: true)
            }
#endif
            .task {
                // Initial load - this will show cached data first if available
                await viewModel.loadContent()
#if os(tvOS)
                // Set initial focus to live section if available
                if !viewModel.followerLives.isEmpty {
                    focusSection = .live
                }
#endif
            }
            .onAppear {
                isViewActive = true
                // Don't load content here - .task already handles initial load
                // This prevents duplicate loading when view appears
            }
            .onDisappear {
                isViewActive = false
            }
            .onChange(of: scenePhase) { _, newPhase in
                if newPhase == .active && isViewActive {
                    Task {
                        // Only load content when returning from background
                        // The view model will handle the refresh interval logic
                        await viewModel.loadContent()
                    }
                }
            }
            .fullScreenCover(item: $selectedLiveStream) { stream in
                LiveDetailView(stream: stream, viewModel: .init(
                    channelService: channelService
                ))
            }
            .fullScreenCover(item: $selectedVideo) { video in
                VideoDetailView(video: video, viewModel: .init(
                    videoService: videoService
                ))
            }
            .onChange(of: selectedVideo) { _, newValue in
                if newValue != nil {
#if os(tvOS)
                    // Store current focus when presenting
                    previousFocusSection = focusSection
#endif
                } else {
                    // Restore previous focus when dismissed
                    Task {
                        await viewModel.loadVideos()
#if os(tvOS)
                        focusSection = previousFocusSection
#endif
                    }
                }
            }
            .alert("Sign Out", isPresented: $showSignOutAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Sign Out", role: .destructive) {
                    viewModel.loginViewModel.signOut()
                }
            } message: {
                Text("Are you sure you want to sign out?")
            }
        }
    }
    
    @ViewBuilder
    private var contentView: some View {
        if case .error(let error) = viewModel.state {
            ErrorView(
                title: "Something Went Wrong",
                message: error.localizedDescription,
                isRetrying: false) {
                    Task {
                        // Force refresh on retry
                        await viewModel.loadContent(forceRefresh: true)
                    }
                }
        } else if case .loaded = viewModel.state, viewModel.isEmpty {
            EmptyHomeView()
        } else {
            contentSections
        }
    }
    
    @ViewBuilder
    private var contentSections: some View {
        VStack(alignment: .leading, spacing: Constants.homeContentSectionSpacing) {
            if !viewModel.followerLives.isEmpty {
                    Text("Live Now")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    LiveStreamHScrollView(
                        streams: viewModel.followerLives,
                        selectedStream: $selectedLiveStream,
                        onUnfollowed: { channel in
                            viewModel.unfollow(channel)
                        }
                    )
#if os(tvOS)
                .focused($focusSection, equals: .live)
#endif
            }
            
            if !viewModel.followerVideos.isEmpty {
                    Text("Latest Videos")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    VideoHScrollView(
                        videos: viewModel.followerVideos,
                        selectedVideo: $selectedVideo,
                        onUnfollowed: { channel in
                            viewModel.unfollow(channel)
                        }
                    )
#if os(tvOS)
                .focused($focusSection, equals: .vod)
#endif
            }
            
            if !viewModel.followers.isEmpty {
                Text("Following")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)
                    .frame(maxWidth: .infinity, alignment: .leading)

                ChannelHScrollView(
                    channels: viewModel.followers,
                    onSelectedChannel: { channel in
                        NotificationCenter.default.post(
                            name: .navigateToChannel,
                            object: channel.id
                        )
                    },
                    onUnfollowed: { channel in
                        viewModel.unfollow(channel)
                    }
                )
#if os(tvOS)
                .focused($focusSection, equals: .followers)
#endif
            }
        }
    }
}

// MARK: - Profile Icon Component

private struct ProfileIconView: View {
    let loginViewModel: any LoginViewModelProtocol
    @Binding var showSignOutAlert: Bool

    var body: some View {
        Menu {
            if let nickname = loginViewModel.profileNickname {
                Text(nickname)
                    .font(.headline)
            }

            Divider()

            Button("Sign Out", role: .destructive) {
                showSignOutAlert = true
            }
        } label: {
            Image(systemName: "person.crop.circle.fill")
        }
    }
}


#Preview("Live + Video") {
    let mockProfile = MockUserProfile(nickname: "Test User", profileImageUrl: "https://example.com/profile.jpg")
    let loginViewModel = MockLoginViewModel(isLoggedIn: true, userProfile: mockProfile)

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}

#Preview("Video") {
    let mockProfile = MockUserProfile(nickname: "Test User", profileImageUrl: "https://example.com/profile.jpg")
    let loginViewModel = MockLoginViewModel(isLoggedIn: true, userProfile: mockProfile)

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(liveCount: 0),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}

#Preview("Channel") {
    let mockProfile = MockUserProfile(nickname: "Test User", profileImageUrl: "https://example.com/profile.jpg")
    let loginViewModel = MockLoginViewModel(isLoggedIn: true, userProfile: mockProfile)

    return HomeView(
        channelService: PreviewChannelService(videoCount: 0, channelCount: 5),
        liveService: PreviewLiveService(liveCount: 0),
        videoService: PreviewVideoService(videoCount: 0),
        loginViewModel: loginViewModel
    )
}

#Preview("Empty State") {
    let mockProfile = MockUserProfile(nickname: "Empty User", profileImageUrl: nil)
    let loginViewModel = MockLoginViewModel(isLoggedIn: true, userProfile: mockProfile)

    return HomeView(
        channelService: PreviewChannelService(videoCount: 0, channelCount: 0),
        liveService: PreviewLiveService(liveCount: 0),
        videoService: PreviewVideoService(videoCount: 0),
        loginViewModel: loginViewModel
    )
}

#Preview("Error State") {
    let loginViewModel = QRCodeLoginViewModel()
    loginViewModel.isUserLoggedIn = true

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}
